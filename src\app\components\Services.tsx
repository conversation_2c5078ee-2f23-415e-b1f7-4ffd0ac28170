'use client';
import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
gsap.registerPlugin(ScrollTrigger);

const ORANGE = "#e8561c";

export default function Services() {
  // Responsive Font Size wie in Work/About
  const getResponsiveFontSize = (base: number) => `clamp(${base * 0.5}px, ${base * 0.08}vw, ${base}px)`;
  const getResponsiveSpacing = (base: number) => `clamp(${base * 0.5}px, ${base * 0.1}vw, ${base}px)`;

  const cardsRef = useRef<Array<HTMLDivElement | null>>([]);

  // Keine Animation mehr, Karten liegen nur als Fächer

  useEffect(() => {
    if (!cardsRef.current) return;
    const cards = cardsRef.current;
    // Pin-&-Flip-Animation: Pinne card-area, Timeline animiert Cards synchron zum Scroll
    const cardArea = document.getElementById('card-area');
    if (cardArea && cards[0] && cards[1] && cards[2]) {
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: cardArea,
          start: 'center center',
          end: '+=650',
          pin: true,
          scrub: true,
        }
      });
      // Card 1 flippt und richtet sich aus
      tl.to(cards[0].querySelector('.card-inner'), { rotateY: 180, rotateZ: 0, x: -570, y: 0, duration: 0.13, ease: 'power2.inOut' }, 0.2); // 0.2-0.33
      // Card 2 flippt und richtet sich aus
      tl.to(cards[1].querySelector('.card-inner'), { rotateY: 180, rotateZ: 0, x: 0, y: 0, duration: 0.13, ease: 'power2.inOut' }, 0.33); // 0.33-0.46
      // Card 3 flippt und richtet sich aus
      tl.to(cards[2].querySelector('.card-inner'), { rotateY: 180, rotateZ: 0, x: 570, y: 0, duration: 0.2, ease: 'power2.inOut' }, 0.46); // 0.46-0.66
      // Am Ende: Alle Cards zIndex 10, damit sie nebeneinander stehen
      tl.set(cards[0], { zIndex: 10 }, 0.66);
      tl.set(cards[1], { zIndex: 10 }, 0.66);
      tl.set(cards[2], { zIndex: 10 }, 0.66);
    }
    // Subtiler, unregelmäßiger Schwebeeffekt (y-Animation, kein scale mehr)
    const tweens: gsap.core.Tween[] = [];
    [0, 1, 2].forEach((i) => {
      const card = cards[i];
      if (!card) return;
      let yFrom = -4, yTo = 4, duration = 4, delay = 0;
      if (i === 1) { yFrom = 4; yTo = -4; duration = 4.5; delay = 0.7; }
      if (i === 2) { yFrom = -3; yTo = 3; duration = 4.2; delay = 1.2; }
      tweens.push(
        gsap.fromTo(card, { y: yFrom }, {
          y: yTo,
          duration,
          yoyo: true,
          repeat: -1,
          ease: 'sine.inOut',
          delay,
        })
      );
    });
    return () => {
      tweens.forEach(t => t.kill());
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section
      className="bg-black text-white w-full flex items-center justify-center rounded-t-[120px] lg:rounded-t-[120px] md:rounded-t-[60px] sm:rounded-t-[40px]"
      style={{
        height: '260vh',
        zIndex: 30,
        position: 'relative',
        transition: 'border-radius 0.4s cubic-bezier(0.4,0,0.2,1)',
        fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
      }}
    >
      {/* Top left label - SERVICES */}
      <div style={{
        position: 'absolute',
        top: 120,
        left: 40,
        color: ORANGE,
        fontSize: 28,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (SERVICES)
      </div>
      {/* Right label - 04 in orange, vertikal mittig wie in Work.tsx */}
      <div style={{
        position: 'absolute',
        top: 'clamp(420px, 50vh, 580px)',
        right: 'clamp(20px, 3vw, 40px)',
        color: ORANGE,
        fontSize: getResponsiveFontSize(42),
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (04)
      </div>
      <div style={{
        maxWidth: 900,
        margin: '0 auto',
        padding: 'clamp(20px, 3vw, 40px)',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        marginTop: -280,
        minHeight: '100vh',
        position: 'relative',
      }}>
        <div style={{ padding: 0 }}>
          <div style={{
            color: '#fff',
            fontSize: getResponsiveFontSize(44),
            fontWeight: 700,
            margin: '-20px 0 48px 0',
            maxWidth: 900,
            alignSelf: 'flex-start',
            lineHeight: 1.15,
          }}>
            From digital strategy to pixel-perfect implementation, I help brands and businesses turn ideas into real, usable products.<br />
            <span style={{
              fontWeight: 400,
              fontSize: getResponsiveFontSize(24),
              display: 'block',
              marginTop: getResponsiveSpacing(24),
              lineHeight: 1.2,
            }}>
              My focus: clarity, usability, and a touch of boldness.
            </span>
          </div>
        </div>
        {/* Kartenbereich */}
        <div id="card-area" style={{ marginTop: getResponsiveSpacing(320), position: 'relative', width: 480, height: 680, perspective: '2000px', marginLeft: 'auto', marginRight: 'auto' }}>
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              ref={el => { cardsRef.current[i] = el; }}
              className="card"
              style={{
                width: '100%',
                height: '100%',
                borderRadius: 18,
                position: 'absolute',
                top: 0,
                left: 0,
                zIndex: i === 0 ? 12 : i === 1 ? 11 : 10,
                boxShadow: '0 8px 32px rgba(0,0,0,0.10)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'transparent',
                border: 'none',
                fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
                transition: 'none',
                willChange: 'transform',
                perspective: '1200px',
              }}
            >
              <div
                className="card-inner"
                style={{
                  width: '100%',
                  height: '100%',
                  position: 'relative',
                  transformStyle: 'preserve-3d',
                  transition: 'transform 0.7s cubic-bezier(0.4,0,0.2,1)',
                  borderRadius: 18,
                  willChange: 'transform',
                  transform: `rotateY(0deg) rotateZ(${i === 0 ? -15 : i === 2 ? 15 : 0}deg) translateX(${i === 0 ? -60 : i === 2 ? 60 : 0}px)`,
                }}
              >
                {/* Vorderseite */}
                <div
                  className="card-front"
                  style={{
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    backfaceVisibility: 'hidden',
                    background: '#fff',
                    borderRadius: 18,
                    display: 'flex',
                    alignItems: 'flex-start',
                    justifyContent: 'flex-start',
                    fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
                  }}
                >
                  {/* SVG mittig platzieren */}
                  <div style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    {i === 0 && (
                      <img src="/svg/puzzle-game-piece-svgrepo-com.svg" alt="Puzzle Piece" style={{ width: 88, height: 88 }} />
                    )}
                    {i === 1 && (
                      <img src="/svg/layers-svgrepo-com.svg" alt="Layers" style={{ width: 88, height: 88 }} />
                    )}
                    {i === 2 && (
                      <img src="/svg/code-svgrepo-com.svg" alt="Code" style={{ width: 88, height: 88 }} />
                    )}
                  </div>
                  {/* Links oben: Titel */}
                  <span style={{
                    position: 'absolute',
                    top: 32,
                    left: 36,
                    color: '#111',
                    fontSize: 22,
                    fontWeight: 600,
                    letterSpacing: 1.2,
                    opacity: 0.85,
                  }}>
                    {i === 0 ? 'Strategy' : i === 1 ? 'Design' : 'Development'}
                  </span>
                  {/* Rechts unten: Titel um 180° gedreht */}
                  <span style={{
                    position: 'absolute',
                    bottom: 32,
                    right: 36,
                    color: '#111',
                    fontSize: 22,
                    fontWeight: 600,
                    letterSpacing: 1.2,
                    opacity: 0.85,
                    transform: 'rotate(180deg)',
                  }}>
                    {i === 0 ? 'Strategy' : i === 1 ? 'Design' : 'Development'}
                  </span>
                  {/* SVG Outline rechts oben */}
                  {i === 0 && (
                    <img src="/svg/puzzle-game-piece-svgrepo-com.svg" alt="Puzzle Piece" style={{ position: 'absolute', top: 32, right: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                  {i === 1 && (
                    <img src="/svg/layers-svgrepo-com.svg" alt="Layers" style={{ position: 'absolute', top: 32, right: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                  {i === 2 && (
                    <img src="/svg/code-svgrepo-com.svg" alt="Code" style={{ position: 'absolute', top: 32, right: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                  {/* SVG Outline links unten */}
                  {i === 0 && (
                    <img src="/svg/puzzle-game-piece-svgrepo-com.svg" alt="Puzzle Piece" style={{ position: 'absolute', bottom: 32, left: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                  {i === 1 && (
                    <img src="/svg/layers-svgrepo-com.svg" alt="Layers" style={{ position: 'absolute', bottom: 32, left: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                  {i === 2 && (
                    <img src="/svg/code-svgrepo-com.svg" alt="Code" style={{ position: 'absolute', bottom: 32, left: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                </div>
                {/* Rückseite */}
                <div
                  className="card-back"
                  style={{
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    backfaceVisibility: 'hidden',
                    background: '#fff',
                    borderRadius: 18,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
                    transform: 'rotateY(180deg)',
                  }}
                >
                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 10 }}>
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0, width: 320, margin: '0 auto' }}>
                      {i === 0 && [
                        'Research & Insights',
                        'Product Vision & Roadmapping',
                        'Business & Tech Consulting',
                        'User & Market Analysis',
                        'Risk Assessment & Planning',
                      ].map((text, idx, arr) => [
                        <div key={text} style={{ color: '#222', fontWeight: 500, fontSize: 20, textAlign: 'center', padding: '16px 0' }}>{text}</div>,
                        idx < arr.length - 1 && <div key={text + '-line'} style={{ width: 120, height: 2, background: ORANGE, borderRadius: 1, margin: '0 auto' }} />
                      ])}
                      {i === 1 && [
                        'UX/UI Design',
                        'Wireframing & Prototyping',
                        'Design Systems & Guidelines',
                        'Interaction & Motion Design',
                        'Visual QA & Handoff',
                      ].map((text, idx, arr) => [
                        <div key={text} style={{ color: '#222', fontWeight: 500, fontSize: 20, textAlign: 'center', padding: '16px 0' }}>{text}</div>,
                        idx < arr.length - 1 && <div key={text + '-line'} style={{ width: 120, height: 2, background: ORANGE, borderRadius: 1, margin: '0 auto' }} />
                      ])}
                      {i === 2 && [
                        'Frontend & Backend Engineering',
                        'Custom Software Solutions',
                        'API Design & Integration',
                        'Testing & Quality Assurance',
                        'Deployment & Maintenance',
                      ].map((text, idx, arr) => [
                        <div key={text} style={{ color: '#222', fontWeight: 500, fontSize: 20, textAlign: 'center', padding: '16px 0' }}>{text}</div>,
                        idx < arr.length - 1 && <div key={text + '-line'} style={{ width: 120, height: 2, background: ORANGE, borderRadius: 1, margin: '0 auto' }} />
                      ])}
                    </div>
                  </div>
                  {/* SVG rechts oben auf Rückseite */}
                  {i === 0 && (
                    <img src="/svg/puzzle-game-piece-svgrepo-com.svg" alt="Puzzle Piece" style={{ position: 'absolute', top: 32, right: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                  {i === 1 && (
                    <img src="/svg/layers-svgrepo-com.svg" alt="Layers" style={{ position: 'absolute', top: 32, right: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                  {i === 2 && (
                    <img src="/svg/code-svgrepo-com.svg" alt="Code" style={{ position: 'absolute', top: 32, right: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                  {/* SVG links unten auf Rückseite */}
                  {i === 0 && (
                    <img src="/svg/puzzle-game-piece-svgrepo-com.svg" alt="Puzzle Piece" style={{ position: 'absolute', bottom: 32, left: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                  {i === 1 && (
                    <img src="/svg/layers-svgrepo-com.svg" alt="Layers" style={{ position: 'absolute', bottom: 32, left: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                  {i === 2 && (
                    <img src="/svg/code-svgrepo-com.svg" alt="Code" style={{ position: 'absolute', bottom: 32, left: 36, width: 32, height: 32, opacity: 1 }} />
                  )}
                  {/* Card-Name oben links auf Rückseite */}
                  <span style={{
                    position: 'absolute',
                    top: 32,
                    left: 36,
                    color: '#111',
                    fontSize: 22,
                    fontWeight: 600,
                    letterSpacing: 1.2,
                    opacity: 0.85,
                  }}>
                    {i === 0 ? 'Strategy' : i === 1 ? 'Design' : 'Development'}
                  </span>
                  {/* Card-Name unten rechts um 180° gedreht auf Rückseite */}
                  <span style={{
                    position: 'absolute',
                    bottom: 32,
                    right: 36,
                    color: '#111',
                    fontSize: 22,
                    fontWeight: 600,
                    letterSpacing: 1.2,
                    opacity: 0.85,
                    transform: 'rotate(180deg)',
                  }}>
                    {i === 0 ? 'Strategy' : i === 1 ? 'Design' : 'Development'}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

